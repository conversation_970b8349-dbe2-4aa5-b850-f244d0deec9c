<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Dashboard';
$site_name = getBankName(); // Get bank name from settings

// Get comprehensive user data and banking information
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get complete user account information
    $user_sql = "SELECT id, account_number, username, email, first_name, last_name, phone, address,
                        date_of_birth, occupation, marital_status, gender, currency, account_type,
                        balance, status, kyc_status, created_at, last_login
                 FROM accounts WHERE id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();

    // Update session with fresh balance
    $_SESSION['balance'] = $user['balance'];
    $current_balance = $user['balance'];

    // Get user's recent transactions
    $sql = "SELECT t.*,
                   CASE
                       WHEN t.sender_id = ? THEN 'sent'
                       ELSE 'received'
                   END as direction,
                   CASE
                       WHEN t.sender_id = ? THEN t.recipient_name
                       ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                   END as other_party
            FROM transfers t
            WHERE (t.sender_id = ? OR t.recipient_id = ?)
            AND t.status = 'completed'
            ORDER BY t.created_at DESC
            LIMIT 5";

    $recent_transactions = $db->query($sql, [$user_id, $user_id, $user_id, $user_id]);

    // Get comprehensive monthly transaction summary
    $monthly_sql = "SELECT
                        COUNT(*) as total_transactions,
                        SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                        SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received,
                        AVG(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as avg_sent,
                        AVG(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as avg_received
                    FROM transfers
                    WHERE (sender_id = ? OR recipient_id = ?)
                    AND status = 'completed'
                    AND MONTH(created_at) = MONTH(CURRENT_DATE())
                    AND YEAR(created_at) = YEAR(CURRENT_DATE())";

    $monthly_result = $db->query($monthly_sql, [$user_id, $user_id, $user_id, $user_id, $user_id, $user_id]);
    $monthly_stats = $monthly_result->fetch_assoc();

    // Calculate previous month for comparison
    $prev_monthly_sql = "SELECT
                            COUNT(*) as total_transactions,
                            SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                            SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received
                        FROM transfers
                        WHERE (sender_id = ? OR recipient_id = ?)
                        AND status = 'completed'
                        AND MONTH(created_at) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH)
                        AND YEAR(created_at) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)";

    $prev_monthly_result = $db->query($prev_monthly_sql, [$user_id, $user_id, $user_id, $user_id]);
    $prev_monthly_stats = $prev_monthly_result->fetch_assoc();

    // Calculate percentage changes
    $income_change = 0;
    $spend_change = 0;
    if ($prev_monthly_stats['total_received'] > 0) {
        $income_change = (($monthly_stats['total_received'] - $prev_monthly_stats['total_received']) / $prev_monthly_stats['total_received']) * 100;
    }
    if ($prev_monthly_stats['total_sent'] > 0) {
        $spend_change = (($monthly_stats['total_sent'] - $prev_monthly_stats['total_sent']) / $prev_monthly_stats['total_sent']) * 100;
    }

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $recent_transactions = null;
    $monthly_stats = ['total_transactions' => 0, 'total_sent' => 0, 'total_received' => 0];
    $prev_monthly_stats = ['total_transactions' => 0, 'total_sent' => 0, 'total_received' => 0];
    $income_change = 0;
    $spend_change = 0;
    $user = $_SESSION; // Fallback to session data
}

?>

<?php
// Include the modular header
include '../includes/dashboard/header.php';
?>

<?php
// Include the modular sidebar
include '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <h1>Dashboard</h1>
            <div class="top-bar-actions">
                <a href="../transfers/" class="btn-outline">Create New</a>
                <a href="../transfers/" class="btn-outline">Add Funds</a>
                <a href="../transfers/" class="btn-primary">Move Money</a>
                <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                    <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Main Section -->
            <div class="main-section">
                <!-- Account Overview Section -->
                <div class="account-overview-section">
                    <!-- Account Summary Card -->
                    <div class="account-summary-card">
                        <div class="account-header">
                            <div class="account-info">
                                <div class="account-title">
                                    <h2><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h2>
                                    <p class="account-subtitle">Account #<?php echo htmlspecialchars($user['account_number']); ?></p>
                                </div>
                                <div class="account-status">
                                    <span class="status-badge status-<?php echo $user['status']; ?>">
                                        <?php echo ucfirst($user['status']); ?>
                                    </span>
                                    <span class="kyc-badge kyc-<?php echo $user['kyc_status']; ?>">
                                        <?php
                                        $kyc_labels = [
                                            'pending' => 'KYC Pending',
                                            'verified' => 'KYC Verified',
                                            'rejected' => 'KYC Rejected'
                                        ];
                                        echo $kyc_labels[$user['kyc_status']] ?? 'KYC Unknown';
                                        ?>
                                    </span>
                                </div>
                            </div>
                            <div class="account-avatar">
                                <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                            </div>
                        </div>

                        <div class="balance-display">
                            <div class="balance-main">
                                <p class="balance-label">Available Balance</p>
                                <h1 class="balance-amount"><?php echo formatCurrency($current_balance, $user['currency'] ?? 'USD'); ?></h1>
                                <p class="balance-details">
                                    <?php echo ucfirst($user['account_type']); ?> Account • <?php echo $user['currency'] ?? 'USD'; ?>
                                </p>
                            </div>
                            <div class="balance-actions">
                                <a href="../transfers/" class="action-btn primary">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                    </svg>
                                    Transfer Money
                                </a>
                                <a href="../cards/" class="action-btn secondary">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                                    </svg>
                                    Manage Cards
                                </a>
                                <a href="../statements/" class="action-btn secondary">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                    </svg>
                                    Statements
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Account Details Grid -->
                    <div class="account-details-grid">
                        <div class="detail-card">
                            <div class="detail-icon">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="detail-content">
                                <h4>Account Type</h4>
                                <p><?php echo ucfirst($user['account_type']); ?> Account</p>
                            </div>
                        </div>
                        <div class="detail-card">
                            <div class="detail-icon">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="detail-content">
                                <h4>KYC Status</h4>
                                <p><?php echo ucfirst($user['kyc_status']); ?></p>
                            </div>
                        </div>
                        <div class="detail-card">
                            <div class="detail-icon">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="detail-content">
                                <h4>Member Since</h4>
                                <p><?php echo date('M Y', strtotime($user['created_at'])); ?></p>
                            </div>
                        </div>
                        <div class="detail-card">
                            <div class="detail-icon">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="detail-content">
                                <h4>Last Login</h4>
                                <p><?php echo $user['last_login'] ? date('M j, Y', strtotime($user['last_login'])) : 'First time'; ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <?php
                        // Get recent beneficiaries for quick actions
                        try {
                            $beneficiaries_sql = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY created_at DESC LIMIT 6";
                            $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
                            $beneficiaries = [];
                            if ($beneficiaries_result) {
                                while ($row = $beneficiaries_result->fetch_assoc()) {
                                    $beneficiaries[] = $row;
                                }
                            }
                        } catch (Exception $e) {
                            $beneficiaries = [];
                        }

                        // Default quick actions if no beneficiaries
                        $default_actions = [
                            ['name' => 'Transfer', 'url' => '../transfers/', 'color' => '#f59e0b', 'amount' => $monthly_stats['total_sent'] ?? 0],
                            ['name' => 'Cards', 'url' => '../cards/', 'color' => '#8b5cf6', 'amount' => $monthly_stats['total_received'] ?? 0],
                            ['name' => 'Analytics', 'url' => '../dashboard/insights.php', 'color' => '#10b981', 'amount' => ($monthly_stats['total_sent'] ?? 0) + ($monthly_stats['total_received'] ?? 0)],
                            ['name' => 'Rewards', 'url' => '../dashboard/rewards.php', 'color' => '#ef4444', 'amount' => $monthly_stats['total_transactions'] ?? 0],
                            ['name' => 'Help', 'url' => '../dashboard/help.php', 'color' => '#6b7280', 'amount' => rand(1000, 5000)],
                            ['name' => 'Add New', 'url' => '../transfers/', 'color' => '#d1d5db', 'amount' => rand(100, 999)]
                        ];

                        $actions_to_show = !empty($beneficiaries) ? array_slice($beneficiaries, 0, 6) : $default_actions;
                        ?>

                        <?php foreach ($actions_to_show as $index => $action): ?>
                        <a href="<?php echo isset($action['url']) ? $action['url'] : '../transfers/'; ?>" class="quick-action">
                            <div class="quick-action-icon" style="background: <?php echo isset($action['color']) ? $action['color'] : '#6366f1'; ?>;">
                                <?php if (isset($action['name']) && !isset($action['account_number'])): ?>
                                    <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                        <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                    </svg>
                                <?php else: ?>
                                    <?php echo strtoupper(substr($action['name'] ?? 'U', 0, 2)); ?>
                                <?php endif; ?>
                            </div>
                            <?php if (isset($action['account_number'])): ?>
                                <?php echo htmlspecialchars($action['name']); ?><br>
                                <small style="opacity: 0.8;"><?php echo htmlspecialchars($action['bank_name'] ?? 'Bank'); ?></small>
                            <?php else: ?>
                                <?php echo htmlspecialchars($action['name']); ?><br>
                                $<?php echo number_format($action['amount'] ?? 0, 0); ?>
                            <?php endif; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Monthly Financial Summary -->
                <div class="financial-summary-grid">
                    <div class="summary-card income">
                        <div class="summary-header">
                            <div class="summary-icon">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="summary-info">
                                <h4>Money In</h4>
                                <p>This Month</p>
                            </div>
                        </div>
                        <div class="summary-amount">
                            <?php echo formatCurrency($monthly_stats['total_received'] ?? 0, $user['currency'] ?? 'USD'); ?>
                        </div>
                        <div class="summary-change <?php echo $income_change >= 0 ? 'positive' : 'negative'; ?>">
                            <?php
                            if ($income_change != 0) {
                                echo ($income_change > 0 ? '+' : '') . number_format($income_change, 1) . '% ';
                                echo $income_change > 0 ? '↗' : '↘';
                            } else {
                                echo 'No change';
                            }
                            ?>
                        </div>
                        <div class="summary-details">
                            <?php echo number_format($monthly_stats['total_transactions'] ?? 0); ?> transactions
                        </div>
                    </div>

                    <div class="summary-card expense">
                        <div class="summary-header">
                            <div class="summary-icon">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293 7.707a1 1 0 011.414 0L9 9.414V17a1 1 0 11-2 0V9.414L5.707 10.707a1 1 0 01-1.414-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="summary-info">
                                <h4>Money Out</h4>
                                <p>This Month</p>
                            </div>
                        </div>
                        <div class="summary-amount">
                            <?php echo formatCurrency($monthly_stats['total_sent'] ?? 0, $user['currency'] ?? 'USD'); ?>
                        </div>
                        <div class="summary-change <?php echo $spend_change <= 0 ? 'positive' : 'negative'; ?>">
                            <?php
                            if ($spend_change != 0) {
                                echo ($spend_change > 0 ? '+' : '') . number_format($spend_change, 1) . '% ';
                                echo $spend_change > 0 ? '↗' : '↘';
                            } else {
                                echo 'No change';
                            }
                            ?>
                        </div>
                        <div class="summary-details">
                            Average: <?php echo formatCurrency($monthly_stats['avg_sent'] ?? 0, $user['currency'] ?? 'USD'); ?>
                        </div>
                    </div>

                    <div class="summary-card net">
                        <div class="summary-header">
                            <div class="summary-icon">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="summary-info">
                                <h4>Net Flow</h4>
                                <p>This Month</p>
                            </div>
                        </div>
                        <div class="summary-amount">
                            <?php
                            $net_flow = ($monthly_stats['total_received'] ?? 0) - ($monthly_stats['total_sent'] ?? 0);
                            echo formatCurrency($net_flow, $user['currency'] ?? 'USD');
                            ?>
                        </div>
                        <div class="summary-change <?php echo $net_flow >= 0 ? 'positive' : 'negative'; ?>">
                            <?php echo $net_flow >= 0 ? 'Positive Flow' : 'Negative Flow'; ?>
                        </div>
                        <div class="summary-details">
                            Balance Impact
                        </div>
                    </div>
                </div>

                <!-- Transaction History -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Transaction history</h3>
                        <div style="display: flex; gap: 1rem; align-items: center;">
                            <span style="font-size: 0.875rem; color: #6b7280;">Last 30 days</span>
                            <button style="background: none; border: 1px solid #d1d5db; padding: 0.5rem 1rem; border-radius: 6px; font-size: 0.875rem;">Filter</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($recent_transactions && $recent_transactions->num_rows > 0): ?>
                            <?php while ($transaction = $recent_transactions->fetch_assoc()): ?>
                                <div class="transaction-item">
                                    <div class="transaction-icon" style="background: <?php echo $transaction['direction'] === 'sent' ? '#ef4444' : '#10b981'; ?>;">
                                        <?php echo strtoupper(substr($transaction['other_party'] ?? 'U', 0, 2)); ?>
                                    </div>
                                    <div class="transaction-content">
                                        <div class="transaction-title"><?php echo htmlspecialchars($transaction['other_party'] ?? 'Unknown'); ?></div>
                                        <div class="transaction-subtitle"><?php echo ucfirst($transaction['direction']); ?> • <?php echo htmlspecialchars($transaction['description'] ?? ''); ?></div>
                                    </div>
                                    <div>
                                        <div class="transaction-amount <?php echo $transaction['direction'] === 'sent' ? 'amount-negative' : 'amount-positive'; ?>">
                                            <?php echo $transaction['direction'] === 'sent' ? '-' : '+'; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                        </div>
                                        <div class="transaction-date"><?php echo date('M d, Y', strtotime($transaction['created_at'])); ?></div>
                                        <div class="status-completed">Completed</div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div style="padding: 2rem; text-align: center; color: #6b7280;">
                                No transactions found
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar Section -->
            <div class="sidebar-section">
                <!-- My Cards -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">My Cards</h3>
                        <a href="../cards/" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">Add New +</a>
                    </div>
                    <div style="padding: 1.5rem;">
                        <?php
                        // Get user's virtual cards
                        try {
                            $cards_sql = "SELECT * FROM virtual_cards WHERE user_id = ? AND status = 'active' LIMIT 1";
                            $cards_result = $db->query($cards_sql, [$user_id]);
                            $user_card = $cards_result ? $cards_result->fetch_assoc() : null;
                        } catch (Exception $e) {
                            $user_card = null;
                        }
                        ?>

                        <?php if ($user_card): ?>
                        <div class="virtual-card">
                            <div class="card-brand"><?php echo strtoupper($user_card['card_type']); ?></div>
                            <div style="margin-bottom: 1rem;">
                                <div style="font-size: 0.875rem; opacity: 0.8;"><?php echo htmlspecialchars($user_card['card_holder_name']); ?></div>
                            </div>
                            <div class="card-number"><?php echo htmlspecialchars($user_card['card_number']); ?></div>
                            <div class="card-details">
                                <div>
                                    <div style="font-size: 0.75rem; opacity: 0.8;">Exp</div>
                                    <div><?php echo sprintf('%02d/%02d', $user_card['expiry_month'], $user_card['expiry_year'] % 100); ?></div>
                                </div>
                                <div>
                                    <div style="font-size: 0.75rem; opacity: 0.8;">CVV</div>
                                    <div>***</div>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <p>No virtual cards found</p>
                            <a href="../cards/" style="color: #6366f1; text-decoration: none;">Create your first card</a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Conversion Rate -->
                <div class="conversion-card">
                    <h3 class="conversion-title">Conversion Rate</h3>
                    <div class="conversion-row">
                        <div>
                            <div class="conversion-currency">Amount</div>
                        </div>
                        <div style="text-align: right;">
                            <div class="conversion-currency">USD</div>
                        </div>
                    </div>
                    <div class="conversion-row">
                        <div class="conversion-amount">238</div>
                        <div class="conversion-amount" style="text-align: right;">USD</div>
                    </div>
                    <div style="margin: 1rem 0; height: 1px; background: #e5e7eb;"></div>
                    <div class="conversion-row">
                        <div class="conversion-amount">222.13</div>
                        <div class="conversion-amount" style="text-align: right;">EUR</div>
                    </div>
                    <button class="conversion-btn">Convert → 238 USD = 222.13 EUR</button>
                </div>

                <!-- Workflows -->
                <div class="workflows-card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                        <h3 style="margin: 0; font-size: 1.125rem; font-weight: 600;">Workflows</h3>
                        <a href="#" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">+</a>
                    </div>

                    <div class="workflow-item">
                        <div class="workflow-icon">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4z"/>
                                <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="workflow-content">
                            <div class="workflow-title">Transactions</div>
                            <div class="workflow-subtitle">Auto Block</div>
                        </div>
                        <svg width="16" height="16" fill="#6b7280" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>

                    <div class="workflow-item">
                        <div class="workflow-icon">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="workflow-content">
                            <div class="workflow-title">Create order</div>
                            <div class="workflow-subtitle">Looks OK</div>
                        </div>
                        <svg width="16" height="16" fill="#6b7280" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>

                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="#" style="color: #6b7280; font-size: 0.875rem; text-decoration: none;">Upcoming →</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabler JS -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
</body>
</html>
