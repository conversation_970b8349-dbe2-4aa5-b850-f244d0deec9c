<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Dashboard';
$site_name = getBankName(); // Get bank name from settings

// Get user's recent transactions
try {
    $db = getDB();
    $sql = "SELECT t.*,
                   CASE
                       WHEN t.sender_id = ? THEN 'sent'
                       ELSE 'received'
                   END as direction,
                   CASE
                       WHEN t.sender_id = ? THEN t.recipient_name
                       ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                   END as other_party
            FROM transfers t
            WHERE (t.sender_id = ? OR t.recipient_id = ?)
            AND t.status = 'completed'
            ORDER BY t.created_at DESC
            LIMIT 5";

    $user_id = $_SESSION['user_id'];
    $recent_transactions = $db->query($sql, [$user_id, $user_id, $user_id, $user_id]);

    // Get account balance (refresh from database)
    $balance_sql = "SELECT balance FROM accounts WHERE id = ?";
    $balance_result = $db->query($balance_sql, [$user_id]);
    $current_balance = $balance_result->fetch_assoc()['balance'];
    $_SESSION['balance'] = $current_balance; // Update session

    // Get monthly transaction summary
    $monthly_sql = "SELECT
                        COUNT(*) as total_transactions,
                        SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                        SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received
                    FROM transfers
                    WHERE (sender_id = ? OR recipient_id = ?)
                    AND status = 'completed'
                    AND MONTH(created_at) = MONTH(CURRENT_DATE())
                    AND YEAR(created_at) = YEAR(CURRENT_DATE())";

    $monthly_result = $db->query($monthly_sql, [$user_id, $user_id, $user_id, $user_id]);
    $monthly_stats = $monthly_result->fetch_assoc();

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $recent_transactions = null;
    $monthly_stats = ['total_transactions' => 0, 'total_sent' => 0, 'total_received' => 0];
}

?>

<?php
// Include the modular header
include '../includes/dashboard/header.php';
?>

<?php
// Include the modular sidebar
include '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <h1>Dashboard</h1>
            <div class="top-bar-actions">
                <a href="../transfers/" class="btn-outline">Create New</a>
                <a href="../transfers/" class="btn-outline">Add Funds</a>
                <a href="../transfers/" class="btn-primary">Move Money</a>
                <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                    <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Main Section -->
            <div class="main-section">
                <!-- Balance Card -->
                <div class="balance-card">
                    <div class="balance-header">
                        <div>
                            <p class="balance-title">Total Balance In USD</p>
                            <h2 class="balance-amount">$<?php echo number_format($current_balance, 2); ?></h2>
                        </div>
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center;">
                            <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>

                    <div class="balance-actions">
                        <a href="../transfers/" class="balance-btn">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                            Send
                        </a>
                        <a href="../transfers/" class="balance-btn">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                            </svg>
                            Request
                        </a>
                        <a href="../transfers/" class="balance-btn">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"/>
                            </svg>
                            Top-Up
                        </a>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <?php
                        // Get recent beneficiaries for quick actions
                        try {
                            $beneficiaries_sql = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY created_at DESC LIMIT 6";
                            $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
                            $beneficiaries = [];
                            if ($beneficiaries_result) {
                                while ($row = $beneficiaries_result->fetch_assoc()) {
                                    $beneficiaries[] = $row;
                                }
                            }
                        } catch (Exception $e) {
                            $beneficiaries = [];
                        }

                        // Default quick actions if no beneficiaries
                        $default_actions = [
                            ['name' => 'Transfer', 'url' => '../transfers/', 'color' => '#f59e0b', 'amount' => $monthly_stats['total_sent'] ?? 0],
                            ['name' => 'Cards', 'url' => '../cards/', 'color' => '#8b5cf6', 'amount' => $monthly_stats['total_received'] ?? 0],
                            ['name' => 'Analytics', 'url' => '../dashboard/insights.php', 'color' => '#10b981', 'amount' => ($monthly_stats['total_sent'] ?? 0) + ($monthly_stats['total_received'] ?? 0)],
                            ['name' => 'Rewards', 'url' => '../dashboard/rewards.php', 'color' => '#ef4444', 'amount' => $monthly_stats['total_transactions'] ?? 0],
                            ['name' => 'Help', 'url' => '../dashboard/help.php', 'color' => '#6b7280', 'amount' => rand(1000, 5000)],
                            ['name' => 'Add New', 'url' => '../transfers/', 'color' => '#d1d5db', 'amount' => rand(100, 999)]
                        ];

                        $actions_to_show = !empty($beneficiaries) ? array_slice($beneficiaries, 0, 6) : $default_actions;
                        ?>

                        <?php foreach ($actions_to_show as $index => $action): ?>
                        <a href="<?php echo isset($action['url']) ? $action['url'] : '../transfers/'; ?>" class="quick-action">
                            <div class="quick-action-icon" style="background: <?php echo isset($action['color']) ? $action['color'] : '#6366f1'; ?>;">
                                <?php if (isset($action['name']) && !isset($action['account_number'])): ?>
                                    <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                        <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                    </svg>
                                <?php else: ?>
                                    <?php echo strtoupper(substr($action['name'] ?? 'U', 0, 2)); ?>
                                <?php endif; ?>
                            </div>
                            <?php if (isset($action['account_number'])): ?>
                                <?php echo htmlspecialchars($action['name']); ?><br>
                                <small style="opacity: 0.8;"><?php echo htmlspecialchars($action['bank_name'] ?? 'Bank'); ?></small>
                            <?php else: ?>
                                <?php echo htmlspecialchars($action['name']); ?><br>
                                $<?php echo number_format($action['amount'] ?? 0, 0); ?>
                            <?php endif; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <p class="stat-title">Total Income</p>
                        <h3 class="stat-value">$<?php echo number_format($monthly_stats['total_received'] ?? 0, 0); ?></h3>
                        <div class="stat-change">+2.5% ↗</div>
                    </div>
                    <div class="stat-card">
                        <p class="stat-title">Total Spend</p>
                        <h3 class="stat-value">$<?php echo number_format($monthly_stats['total_sent'] ?? 0, 0); ?></h3>
                        <div class="stat-change">-2.5% ↘</div>
                    </div>
                </div>

                <!-- Transaction History -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Transaction history</h3>
                        <div style="display: flex; gap: 1rem; align-items: center;">
                            <span style="font-size: 0.875rem; color: #6b7280;">Last 30 days</span>
                            <button style="background: none; border: 1px solid #d1d5db; padding: 0.5rem 1rem; border-radius: 6px; font-size: 0.875rem;">Filter</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($recent_transactions && $recent_transactions->num_rows > 0): ?>
                            <?php while ($transaction = $recent_transactions->fetch_assoc()): ?>
                                <div class="transaction-item">
                                    <div class="transaction-icon" style="background: <?php echo $transaction['direction'] === 'sent' ? '#ef4444' : '#10b981'; ?>;">
                                        <?php echo strtoupper(substr($transaction['other_party'] ?? 'U', 0, 2)); ?>
                                    </div>
                                    <div class="transaction-content">
                                        <div class="transaction-title"><?php echo htmlspecialchars($transaction['other_party'] ?? 'Unknown'); ?></div>
                                        <div class="transaction-subtitle"><?php echo ucfirst($transaction['direction']); ?> • <?php echo htmlspecialchars($transaction['description'] ?? ''); ?></div>
                                    </div>
                                    <div>
                                        <div class="transaction-amount <?php echo $transaction['direction'] === 'sent' ? 'amount-negative' : 'amount-positive'; ?>">
                                            <?php echo $transaction['direction'] === 'sent' ? '-' : '+'; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                        </div>
                                        <div class="transaction-date"><?php echo date('M d, Y', strtotime($transaction['created_at'])); ?></div>
                                        <div class="status-completed">Completed</div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div style="padding: 2rem; text-align: center; color: #6b7280;">
                                No transactions found
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar Section -->
            <div class="sidebar-section">
                <!-- My Cards -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">My Cards</h3>
                        <a href="../cards/" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">Add New +</a>
                    </div>
                    <div style="padding: 1.5rem;">
                        <?php
                        // Get user's virtual cards
                        try {
                            $cards_sql = "SELECT * FROM virtual_cards WHERE user_id = ? AND status = 'active' LIMIT 1";
                            $cards_result = $db->query($cards_sql, [$user_id]);
                            $user_card = $cards_result ? $cards_result->fetch_assoc() : null;
                        } catch (Exception $e) {
                            $user_card = null;
                        }
                        ?>

                        <?php if ($user_card): ?>
                        <div class="virtual-card">
                            <div class="card-brand"><?php echo strtoupper($user_card['card_type']); ?></div>
                            <div style="margin-bottom: 1rem;">
                                <div style="font-size: 0.875rem; opacity: 0.8;"><?php echo htmlspecialchars($user_card['card_holder_name']); ?></div>
                            </div>
                            <div class="card-number"><?php echo htmlspecialchars($user_card['card_number']); ?></div>
                            <div class="card-details">
                                <div>
                                    <div style="font-size: 0.75rem; opacity: 0.8;">Exp</div>
                                    <div><?php echo sprintf('%02d/%02d', $user_card['expiry_month'], $user_card['expiry_year'] % 100); ?></div>
                                </div>
                                <div>
                                    <div style="font-size: 0.75rem; opacity: 0.8;">CVV</div>
                                    <div>***</div>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <p>No virtual cards found</p>
                            <a href="../cards/" style="color: #6366f1; text-decoration: none;">Create your first card</a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Conversion Rate -->
                <div class="conversion-card">
                    <h3 class="conversion-title">Conversion Rate</h3>
                    <div class="conversion-row">
                        <div>
                            <div class="conversion-currency">Amount</div>
                        </div>
                        <div style="text-align: right;">
                            <div class="conversion-currency">USD</div>
                        </div>
                    </div>
                    <div class="conversion-row">
                        <div class="conversion-amount">238</div>
                        <div class="conversion-amount" style="text-align: right;">USD</div>
                    </div>
                    <div style="margin: 1rem 0; height: 1px; background: #e5e7eb;"></div>
                    <div class="conversion-row">
                        <div class="conversion-amount">222.13</div>
                        <div class="conversion-amount" style="text-align: right;">EUR</div>
                    </div>
                    <button class="conversion-btn">Convert → 238 USD = 222.13 EUR</button>
                </div>

                <!-- Workflows -->
                <div class="workflows-card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                        <h3 style="margin: 0; font-size: 1.125rem; font-weight: 600;">Workflows</h3>
                        <a href="#" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">+</a>
                    </div>

                    <div class="workflow-item">
                        <div class="workflow-icon">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4z"/>
                                <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="workflow-content">
                            <div class="workflow-title">Transactions</div>
                            <div class="workflow-subtitle">Auto Block</div>
                        </div>
                        <svg width="16" height="16" fill="#6b7280" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>

                    <div class="workflow-item">
                        <div class="workflow-icon">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="workflow-content">
                            <div class="workflow-title">Create order</div>
                            <div class="workflow-subtitle">Looks OK</div>
                        </div>
                        <svg width="16" height="16" fill="#6b7280" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>

                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="#" style="color: #6b7280; font-size: 0.875rem; text-decoration: none;">Upcoming →</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabler JS -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
</body>
</html>
