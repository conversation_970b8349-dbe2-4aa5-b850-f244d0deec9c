<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title . ' - ' . $site_name); ?></title>

    <!-- Tabler CSS -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons@2.44.0/icons-sprite.svg" rel="stylesheet"/>

    <!-- Custom CSS -->
    <link href="../assets/css/dashboard.css" rel="stylesheet">

    <style>
        body {
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Banking Sidebar Styles */
        .banking-sidebar {
            background: #ffffff;
            width: 320px;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            border-right: 1px solid #e5e7eb;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .bank-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: #6366f1;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #60a5fa;
            backdrop-filter: blur(10px);
        }

        .bank-name {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: #6366f1;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: white;
            font-size: 0.875rem;
        }

        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .account-number {
            font-size: 0.75rem;
            color: #6b7280;
            font-family: 'Courier New', monospace;
        }

        .sidebar-nav {
            flex: 1;
            padding: 1rem 0;
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: #9ca3af;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            padding: 0 1.5rem 0.75rem;
            margin-bottom: 0.5rem;
        }

        .nav-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: #6b7280;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            font-weight: 500;
            position: relative;
        }

        .nav-link:hover {
            background: #f3f4f6;
            color: #6366f1;
            text-decoration: none;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: #6366f1;
            color: white;
            border: 1px solid #6366f1;
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: #60a5fa;
            border-radius: 0 2px 2px 0;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        .sidebar-footer {
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
        }

        .logout-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: #f87171;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            font-weight: 500;
            background: rgba(248, 113, 113, 0.1);
            border: 1px solid rgba(248, 113, 113, 0.2);
        }

        .logout-link:hover {
            background: rgba(248, 113, 113, 0.2);
            color: #f87171;
            text-decoration: none;
            transform: translateX(4px);
        }

        .logout-icon {
            width: 20px;
            height: 20px;
        }

        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            padding: 2rem;
            background: #f8fafc;
        }

        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .top-bar h1 {
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
            color: #1a1a1a;
        }

        .top-bar-actions {
            display: flex;
            gap: 1rem;
        }

        .btn-primary {
            background: #6366f1;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-outline {
            background: transparent;
            border: 1px solid #d1d5db;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            color: #374151;
            font-weight: 500;
            text-decoration: none;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        .main-section {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .sidebar-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .balance-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e5e7eb;
        }

        .balance-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .balance-title {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .balance-amount {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1a1a1a;
            margin: 0.5rem 0;
        }

        .balance-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .balance-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid #e5e7eb;
            background: white;
            color: #374151;
        }

        .quick-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .quick-action {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .quick-action-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .stat-title {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0 0 0.5rem;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0;
        }

        .stat-change {
            font-size: 0.75rem;
            color: #10b981;
            margin-top: 0.25rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0;
        }

        .card-body {
            padding: 0;
        }

        .virtual-card {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .virtual-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
        }

        .card-brand {
            text-align: right;
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 1rem;
        }

        .card-number {
            font-family: 'Courier New', monospace;
            font-size: 1.125rem;
            letter-spacing: 2px;
            margin-bottom: 1rem;
        }

        .card-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
        }

        .conversion-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .conversion-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0 0 1rem;
        }

        .conversion-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .conversion-amount {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .conversion-currency {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .conversion-btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            width: 100%;
            margin-top: 1rem;
        }

        .workflows-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .workflow-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 0.75rem;
            background: #f9fafb;
        }

        .workflow-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #1f2937;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .workflow-content {
            flex: 1;
        }

        .workflow-title {
            font-weight: 600;
            color: #1a1a1a;
            margin: 0 0 0.25rem;
        }

        .workflow-subtitle {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .transaction-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            color: white;
        }

        .transaction-content {
            flex: 1;
        }

        .transaction-title {
            font-weight: 500;
            color: #1a1a1a;
            margin: 0 0 0.25rem;
        }

        .transaction-subtitle {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .transaction-amount {
            font-weight: 600;
            text-align: right;
        }

        .transaction-date {
            font-size: 0.75rem;
            color: #9ca3af;
            text-align: right;
            margin-top: 0.25rem;
        }

        .amount-positive {
            color: #10b981;
        }

        .amount-negative {
            color: #ef4444;
        }

        .status-completed {
            color: #10b981;
            background: #d1fae5;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-pending {
            color: #f59e0b;
            background: #fef3c7;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Banking Sidebar Responsive Design */
        @media (max-width: 1024px) {
            .banking-sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .bank-name {
                font-size: 1.125rem;
            }
        }

        @media (max-width: 768px) {
            .banking-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 280px;
            }

            .banking-sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }

            .sidebar-header {
                padding: 1.5rem;
            }

            .user-info {
                padding: 0.75rem;
            }

            .nav-section-title {
                padding: 0 1rem 0.5rem;
            }

            .nav-item {
                margin: 0.25rem 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .banking-sidebar {
                width: 100%;
            }

            .bank-name {
                font-size: 1rem;
            }

            .user-name {
                font-size: 0.8125rem;
            }

            .account-number {
                font-size: 0.6875rem;
            }
        }
    </style>
</head>
<body>
