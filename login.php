<?php
require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        redirect('admin/');
    } else {
        redirect('dashboard/');
    }
}

$error = '';
$username = '';

// Get site settings for logo, name and favicon
function getSiteSettings() {
    try {
        require_once 'config/database.php';
        $db = getDB();

        $settings = [];
        $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key IN ('site_name', 'site_logo', 'site_favicon')");

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }

        return [
            'site_name' => $settings['site_name'] ?? 'SecureBank Online Banking',
            'site_logo' => $settings['site_logo'] ?? '',
            'site_favicon' => $settings['site_favicon'] ?? ''
        ];
    } catch (Exception $e) {
        error_log("Error getting site settings: " . $e->getMessage());
        return [
            'site_name' => 'SecureBank Online Banking',
            'site_logo' => '',
            'site_favicon' => ''
        ];
    }
}

$site_settings = getSiteSettings();

// Function to determine login method based on input format
function determineLoginMethod($identifier) {
    // Check if it's an email
    if (filter_var($identifier, FILTER_VALIDATE_EMAIL)) {
        return 'email';
    }

    // Check if it's an account number (typically 10-12 digits)
    if (preg_match('/^\d{10,12}$/', $identifier)) {
        return 'account_number';
    }

    // Default to username
    return 'username';
}

// Function to validate login identifier format
function validateLoginIdentifier($identifier, $method) {
    switch ($method) {
        case 'email':
            return filter_var($identifier, FILTER_VALIDATE_EMAIL) !== false;
        case 'account_number':
            return preg_match('/^\d{10,12}$/', $identifier) === 1;
        case 'username':
            return preg_match('/^[a-zA-Z0-9_]{3,30}$/', $identifier) === 1;
        default:
            return false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $login_identifier = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

    // Check for too many failed attempts using dynamic security settings
    $security_settings = getSecuritySettings();
    $max_attempts = $security_settings['login_attempts_limit'] ?? 5;
    $failed_attempts = getFailedLoginAttempts($login_identifier, $ip_address);

    if ($failed_attempts >= $max_attempts) {
        $lockout_duration = $security_settings['lockout_duration'] ?? 30;
        $error = "Too many failed login attempts. Please try again after {$lockout_duration} minutes.";
    } else {
        if (empty($login_identifier) || empty($password)) {
            $error = 'Please enter both login credentials and password.';
        } else {
            // Validate login identifier format
            $login_method = determineLoginMethod($login_identifier);
            if (!validateLoginIdentifier($login_identifier, $login_method)) {
                switch ($login_method) {
                    case 'email':
                        $error = 'Please enter a valid email address.';
                        break;
                    case 'account_number':
                        $error = 'Please enter a valid account number (10-12 digits).';
                        break;
                    case 'username':
                        $error = 'Please enter a valid username (3-30 characters, letters, numbers, and underscores only).';
                        break;
                    default:
                        $error = 'Please enter valid login credentials.';
                        break;
                }
            } else {
                try {
                    $db = getDB();

                    // Determine login method and build appropriate SQL query
                    $login_method = determineLoginMethod($login_identifier);
                    $sql = "SELECT id, username, password, first_name, last_name, email, account_number,
                                  balance, status, is_admin, kyc_status
                            FROM accounts
                            WHERE ";

                    switch ($login_method) {
                        case 'email':
                            $sql .= "email = ?";
                            break;
                        case 'account_number':
                            $sql .= "account_number = ?";
                            break;
                        case 'username':
                        default:
                            $sql .= "username = ?";
                            break;
                    }

                    $result = $db->query($sql, [$login_identifier]);
                
                if ($result && $result->num_rows === 1) {
                    $user = $result->fetch_assoc();
                    
                    if (verifyPassword($password, $user['password'])) {
                        // Check account status
                        if ($user['status'] === 'pending') {
                            $error = 'Your account is pending admin approval. Please wait for activation.';
                        } elseif (!isValidEmail($user['email'])) {
                            $error = 'Your account email address is invalid. Please contact support to update your email address before logging in.';
                        } else {
                            // Check if OTP is enabled for this user
                            $otp_check_sql = "SELECT otp_enabled FROM user_security_settings WHERE user_id = ?";
                            $otp_result = $db->query($otp_check_sql, [$user['id']]);
                            $otp_enabled = true; // Default to enabled for security

                            if ($otp_result && $otp_result->num_rows === 1) {
                                $otp_settings = $otp_result->fetch_assoc();
                                $otp_enabled = ($otp_settings['otp_enabled'] == 1);
                            }

                            if ($otp_enabled) {
                                // Generate and send OTP
                                $otp = generateOTP();
                                $user_name = $user['first_name'] . ' ' . $user['last_name'];

                                // Store OTP first
                                if (storeOTP($user['id'], $otp)) {
                                    // Try to send email
                                    $emailSent = sendOTPEmail($user['email'], $otp, $user_name);

                                    if ($emailSent) {
                                        // Set OTP session variables
                                        $_SESSION['otp_user_id'] = $user['id'];
                                        $_SESSION['otp_pending'] = true;
                                        $_SESSION['otp_email'] = $user['email'];

                                        // Record successful login attempt
                                        recordLoginAttempt($username, true);

                                        // Log activity
                                        logActivity($user['id'], 'User credentials verified, OTP sent to email');

                                        // Redirect to OTP verification
                                        redirect('auth/verify-otp.php');
                                    } else {
                                        // Email failed but OTP is stored - still allow verification
                                        $_SESSION['otp_user_id'] = $user['id'];
                                        $_SESSION['otp_pending'] = true;
                                        $_SESSION['otp_email'] = $user['email'];
                                        $_SESSION['email_failed'] = true;

                                        // Record login attempt
                                        recordLoginAttempt($username, true);

                                        // Log activity
                                        logActivity($user['id'], 'User credentials verified, OTP generated but email failed');

                                        // Redirect to OTP verification with email warning
                                        redirect('auth/verify-otp.php?email_failed=1');
                                    }
                                } else {
                                    $error = 'Failed to generate verification code. Please try again.';
                                }
                            } else {
                                // OTP is disabled, log user in directly
                                $_SESSION['user_id'] = $user['id'];
                                $_SESSION['username'] = $user['username'];
                                $_SESSION['user_logged_in'] = true;
                                $_SESSION['login_time'] = time();

                                // Record successful login
                                recordLoginAttempt($login_identifier, true);

                                // Log activity
                                logActivity($user['id'], 'User logged in successfully (OTP disabled)');

                                // Redirect to user dashboard
                                redirect('dashboard/');
                            }
                        }
                    } else {
                        $error = 'Invalid login credentials.';
                        recordLoginAttempt($login_identifier, false);
                    }
                } else {
                    $error = 'Invalid login credentials.';
                    recordLoginAttempt($login_identifier, false);
                }
                } catch (Exception $e) {
                    error_log("Login error: " . $e->getMessage());
                    $error = 'An error occurred. Please try again.';
                }
            }
        }
    }
}

$page_title = 'User Login';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . htmlspecialchars($site_settings['site_name']); ?></title>

    <!-- Favicon -->
    <?php if (!empty($site_settings['site_favicon']) && file_exists($site_settings['site_favicon'])): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($site_settings['site_favicon']); ?>">
    <?php else: ?>
        <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico">
    <?php endif; ?>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: #1f2937;
        }

        /* Modern Banking Header */
        .banking-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .header-logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .header-logo img {
            max-height: 40px;
            width: auto;
        }

        .header-logo h2 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0;
        }

        .header-nav {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: #6b7280;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .nav-link:hover {
            color: #4f46e5;
        }

        .header-cta {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-outline-primary {
            border: 1px solid #4f46e5;
            color: #4f46e5;
            background: transparent;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-outline-primary:hover {
            background: #4f46e5;
            color: white;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-1px);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding-top: 80px;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-text {
            color: white;
        }

        .hero-text h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
        }

        .hero-text p {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .hero-features {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .hero-feature {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            opacity: 0.9;
        }

        .hero-feature i {
            color: #10b981;
            font-size: 1.1rem;
        }

        .hero-cta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-hero-primary {
            background: white;
            color: #4f46e5;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-hero-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: #4f46e5;
        }

        .btn-hero-secondary {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-hero-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
        }

        /* Login Form Section */
        .login-section {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 480px;
            width: 100%;
        }

        .logo {
            margin-bottom: 40px;
            text-align: left;
            width: 100%;
        }

        .logo img {
            max-width: 200px;
            height: auto;
            max-height: 80px;
            object-fit: contain;
            background: transparent;
            mix-blend-mode: multiply;
        }

        .logo-fallback {
            font-size: 24px;
            font-weight: 700;
            color: #4361ee;
            margin: 0;
        }

        .welcome-text {
            text-align: left;
            margin-bottom: 40px;
            width: 100%;
        }

        .welcome-text h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 10px;
        }

        .welcome-text p {
            color: #6b7280;
            font-size: 16px;
            margin: 0;
        }

        .login-form {
            width: 100%;
            max-width: 400px;
            text-align: left;
        }

        .form-group {
            margin-bottom: 20px;
            width: 100%;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-control {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 12px 16px 12px 45px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: white;
            width: 100%;
            box-sizing: border-box;
        }

        .form-control:focus {
            border-color: #4361ee;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
            outline: none;
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            z-index: 2;
        }

        .btn-login {
            background: #4361ee;
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 16px;
            width: 100%;
            transition: all 0.2s ease;
            cursor: pointer;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-login:hover {
            background: #3651d4;
            transform: translateY(-1px);
        }

        .alert {
            border-radius: 8px;
            border: none;
            padding: 12px 16px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background: #fef2f2;
            color: #dc2626;
            border-left: 4px solid #dc2626;
        }

        .right-panel {
            background: linear-gradient(135deg, #4361ee 0%, #3651d4 50%, #2d46c7 100%);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .right-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('demo-images/Image_fx (8).jpg'), url('demo-images/use_this.jpg');
            background-size: cover, cover;
            background-position: center, center;
            background-repeat: no-repeat, no-repeat;
            background-blend-mode: overlay;
            opacity: 0.08;
            z-index: 1;
        }

        .right-panel::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.2;
            z-index: 2;
        }

        .right-panel-content {
            display: flex;
            flex-direction: column;
            height: 100%;
            z-index: 3;
            position: relative;
            padding: 40px;
        }

        .feature-illustration {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
        }

        .bottom-text {
            flex: 0 0 auto;
            text-align: center;
            color: white;
        }

        .bottom-text h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            opacity: 0.95;
        }

        .bottom-text p {
            font-size: 14px;
            opacity: 0.8;
            margin: 0;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .left-panel {
                padding: 40px 30px;
            }

            .right-panel {
                display: none;
            }

            .welcome-text h1 {
                font-size: 24px;
            }

            .logo img {
                max-width: 150px;
                max-height: 60px;
            }
        }

        @media (max-width: 576px) {
            .left-panel {
                padding: 30px 20px;
            }

            .welcome-text h1 {
                font-size: 22px;
            }
        }

        .forgot-password {
            text-align: right;
            margin-top: 8px;
        }

        .forgot-password a {
            color: #4361ee;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .signup-link {
            text-align: center;
            margin-top: 30px;
            color: #6b7280;
        }

        .signup-link a {
            color: #4361ee;
            text-decoration: none;
            font-weight: 600;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }
        /* Features Section */
        .features-section {
            padding: 5rem 0;
            background: #f8fafc;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .section-header p {
            font-size: 1.25rem;
            color: #6b7280;
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: #6b7280;
            line-height: 1.6;
        }

        /* Security Section */
        .security-section {
            padding: 5rem 0;
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: white;
        }

        .security-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .security-text h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
        }

        .security-text p {
            font-size: 1.125rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .security-features {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .security-feature {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .security-feature i {
            color: #10b981;
            font-size: 1.25rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .security-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .header-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Modern Banking Header -->
    <header class="banking-header">
        <div class="header-content">
            <div class="header-logo">
                <?php if (!empty($site_settings['site_logo']) && file_exists($site_settings['site_logo'])): ?>
                    <img src="<?php echo htmlspecialchars($site_settings['site_logo']); ?>" alt="<?php echo htmlspecialchars($site_settings['site_name']); ?>">
                <?php endif; ?>
                <h2><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
            </div>
            <nav class="header-nav">
                <a href="#features" class="nav-link">Features</a>
                <a href="#security" class="nav-link">Security</a>
                <a href="#mobile" class="nav-link">Mobile Banking</a>
                <a href="#contact" class="nav-link">Contact</a>
            </nav>
            <div class="header-cta">
                <a href="register.php" class="btn-outline-primary">Open Account</a>
                <a href="#login" class="btn-primary">Sign In</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Banking Made Simple & Secure</h1>
                <p>Experience the future of digital banking with our comprehensive suite of financial services. Manage your money, make payments, and achieve your financial goals with confidence.</p>

                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-check-circle"></i>
                        <span>24/7 Online Banking</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-check-circle"></i>
                        <span>Advanced Security</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-check-circle"></i>
                        <span>Mobile Banking App</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-check-circle"></i>
                        <span>Instant Transfers</span>
                    </div>
                </div>

                <div class="hero-cta">
                    <a href="register.php" class="btn-hero-primary">
                        <i class="fas fa-user-plus"></i>
                        Open Your Account
                    </a>
                    <a href="#features" class="btn-hero-secondary">
                        <i class="fas fa-info-circle"></i>
                        Learn More
                    </a>
                </div>
            </div>

            <!-- Login Form -->
            <div class="login-section" id="login">
                <div class="login-header">
                    <h2>Welcome Back</h2>
                    <p>Sign in to access your banking dashboard</p>
                </div>

                <div class="login-tabs">
                    <div class="login-tab active">Personal Banking</div>
                    <div class="login-tab">Business Banking</div>
                </div>

            <!-- Error Messages -->
            <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>

            <?php if (isset($_GET['timeout'])): ?>
            <div class="alert alert-warning">
                <i class="fas fa-clock me-2"></i>
                Your session has expired. Please log in again.
            </div>
            <?php endif; ?>

                <!-- Error Messages -->
                <?php if (!empty($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <?php if (isset($_GET['timeout'])): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-clock"></i>
                    Your session has expired. Please log in again.
                </div>
                <?php endif; ?>

                <!-- Login Form -->
                <form method="post" action="" autocomplete="off" novalidate>
                    <div class="form-group">
                        <label class="form-label" for="username">Username, Email, or Account Number</label>
                        <div class="input-group">
                            <i class="fas fa-user input-icon"></i>
                            <input type="text"
                                   class="form-control"
                                   id="username"
                                   name="username"
                                   placeholder="Enter username, email, or account number"
                                   value="<?php echo htmlspecialchars($login_identifier ?? ''); ?>"
                                   required
                                   autocomplete="username">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="password">Password</label>
                        <div class="input-group">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   placeholder="Enter your password"
                                   required>
                        </div>
                        <div class="forgot-password">
                            <a href="forgot-password.php">Forgot Password?</a>
                        </div>
                    </div>

                    <button type="submit" class="btn-login">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In to Your Account
                    </button>
                </form>

                <div class="signup-link">
                    Don't have an account? <a href="register.php">Open Account</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" id="features">
        <div class="features-container">
            <div class="section-header">
                <h2>Banking Features That Work For You</h2>
                <p>Discover our comprehensive suite of banking services designed to make your financial life easier and more secure.</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile Banking</h3>
                    <p>Bank on the go with our award-winning mobile app. Check balances, transfer money, deposit checks, and pay bills from anywhere.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Advanced Security</h3>
                    <p>Your money and data are protected with bank-level encryption, multi-factor authentication, and real-time fraud monitoring.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <h3>Instant Transfers</h3>
                    <p>Send money instantly to friends, family, or businesses. Transfer between your accounts or to external banks in seconds.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h3>Digital Cards</h3>
                    <p>Get instant access to virtual cards for online shopping and manage all your cards from one secure dashboard.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Financial Insights</h3>
                    <p>Track your spending, set budgets, and get personalized insights to help you achieve your financial goals.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>24/7 Support</h3>
                    <p>Get help when you need it with our round-the-clock customer support via chat, phone, or email.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Security Section -->
    <section class="security-section" id="security">
        <div class="security-content">
            <div class="security-text">
                <h2>Your Security is Our Priority</h2>
                <p>We use the latest technology and industry best practices to keep your money and personal information safe and secure.</p>

                <div class="security-features">
                    <div class="security-feature">
                        <i class="fas fa-lock"></i>
                        <span>256-bit SSL Encryption</span>
                    </div>
                    <div class="security-feature">
                        <i class="fas fa-user-shield"></i>
                        <span>Multi-Factor Authentication</span>
                    </div>
                    <div class="security-feature">
                        <i class="fas fa-eye"></i>
                        <span>Real-time Fraud Monitoring</span>
                    </div>
                    <div class="security-feature">
                        <i class="fas fa-bell"></i>
                        <span>Instant Security Alerts</span>
                    </div>
                </div>
            </div>

            <div class="security-visual">
                <svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8" />
                            <stop offset="100%" style="stop-color:#059669;stop-opacity:0.6" />
                        </linearGradient>
                    </defs>

                    <!-- Main Security Shield -->
                    <path d="M200 50 L240 40 L280 50 L280 120 Q280 160 200 180 Q120 160 120 120 L120 50 Z"
                          fill="url(#shieldGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>

                    <!-- Shield Check Mark -->
                    <path d="M160 115 L185 135 L240 85" stroke="white" stroke-width="4" fill="none" stroke-linecap="round"/>

                    <!-- Floating Security Elements -->
                    <circle cx="80" cy="80" r="8" fill="rgba(255,255,255,0.2)" />
                    <circle cx="320" cy="100" r="6" fill="rgba(255,255,255,0.2)" />
                    <circle cx="60" cy="180" r="5" fill="rgba(255,255,255,0.2)" />
                    <circle cx="340" cy="200" r="7" fill="rgba(255,255,255,0.2)" />

                    <!-- Connection Lines -->
                    <path d="M88 80 Q150 70 160 100" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
                    <path d="M320 106 Q250 90 240 110" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
                </svg>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Login tab switching
        document.querySelectorAll('.login-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.login-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Form validation enhancement
        const form = document.querySelector('form');
        const inputs = form.querySelectorAll('input[required]');

        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.style.borderColor = '#ef4444';
                } else {
                    this.style.borderColor = '#10b981';
                }
            });
        });

        // Header scroll effect
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.banking-header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>
